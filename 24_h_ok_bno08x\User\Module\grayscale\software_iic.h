//#ifndef __SOFTWARE_IIC_H
//#define __SOFTWARE_IIC_H

//#include "stm32f4xx_hal.h"
//#include "main.h"
//#include "gw_grayscale_sensor.h"

///* 软件I2C引脚定义 - 使用PC4/PC5 */
//#define SDA_PIN GRAY_SOFT_SDA_Pin           // PC5
//#define SDA_PORT GRAY_SOFT_SDA_GPIO_Port    // GPIOC
//#define SCL_PIN GRAY_SOFT_SCL_Pin           // PC4
//#define SCL_PORT GRAY_SOFT_SCL_GPIO_Port    // GPIOC

///* 基本I2C操作宏 */
//#define SDA_HIGH() HAL_GPIO_WritePin(SDA_PORT, SDA_PIN, GPIO_PIN_SET)   // SDA置高
//#define SDA_LOW()  HAL_GPIO_WritePin(SDA_PORT, SDA_PIN, GPIO_PIN_RESET) // SDA置低
//#define SCL_HIGH() HAL_GPIO_WritePin(SCL_PORT, SCL_PIN, GPIO_PIN_SET)   // SCL置高
//#define SCL_LOW()  HAL_GPIO_WritePin(SCL_PORT, SCL_PIN, GPIO_PIN_RESET) // SCL置低
//#define READ_SDA() HAL_GPIO_ReadPin(SDA_PORT, SDA_PIN)                  // 读取SDA

///* 延时函数声明 */
//void Delay_us(uint32_t udelay); // 微秒级延时函数

///* 软件I2C基础函数声明 */
//void IIC_Start(void);           // I2C起始信号
//void IIC_Stop(void);            // I2C停止信号
//unsigned char IIC_WaitAck(void);        // 等待应答信号
//void IIC_SendAck(void);         // 发送应答信号
//void IIC_SendNAck(void);        // 发送非应答信号
//unsigned char IIC_SendByte(unsigned char dat);  // 发送一个字节
//unsigned char IIC_RecvByte(void);       // 接收一个字节

///* 应用层接口函数声明 - 与hardware_iic.c保持一致 */
//unsigned char IIC_ReadByte(unsigned char Salve_Address);    // 读取单字节
//unsigned char IIC_ReadBytes(unsigned char Salve_Address, unsigned char Reg_Address, unsigned char *Result, unsigned char len); // 读取多字节
//unsigned char IIC_WriteByte(unsigned char Salve_Address, unsigned char Reg_Address, unsigned char data); // 写入单字节
//unsigned char IIC_WriteBytes(unsigned char Salve_Address, unsigned char Reg_Address, unsigned char *data, unsigned char len); // 写入多字节

///* 灰度传感器专用函数声明 */
//unsigned char Ping(void);                               // 传感器连接检测
//unsigned char IIC_Get_Digtal(void);                     // 获取数字量数据
//unsigned char IIC_Get_Anolog(unsigned char *Result, unsigned char len); // 获取模拟量数据
//unsigned char IIC_Get_Single_Anolog(unsigned char Channel);  // 获取单通道模拟量
//unsigned char IIC_Anolog_Normalize(uint8_t Normalize_channel); // 模拟量归一化
//unsigned short IIC_Get_Offset(void);                    // 获取偏移量

//#endif
