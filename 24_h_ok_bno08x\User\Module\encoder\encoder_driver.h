#ifndef __ENCODER_DRIVER_H__
#define __ENCODER_DRIVER_H__

#include "mydefine.h"

// 编码器每转一圈的脉冲数 (PPR)
#define ENCODER_PPR (500 * 20 * 4) // 13线/相, 20倍减速比, 4倍频
// 车轮直径 (单位: 厘米)
#define WHEEL_DIAMETER_CM 4.5f

// 自动计算周长和采样时间
#define PI 3.14159265f
#define WHEEL_CIRCUMFERENCE_CM (WHEEL_DIAMETER_CM * PI)
#define SAMPLING_TIME_S 0.005 // 采样时间, 与 Scheduler 中的任务周期一致 (10ms)

// 在encoder_app.h中添加滤波系数定义
#define ENCODER_FILTER_ALPHA 0.3f  // 滤波系数(0.0-1.0)，值越小滤波效果越强


/**
 * @brief 编码器数据结构体
 */
// 在encoder结构体中添加滤波后的速度变量
typedef struct {
    TIM_HandleTypeDef *htim;
    unsigned char reverse;
    int16_t count;
    int32_t total_count;
    float speed_cm_s;
    float filtered_speed_cm_s;  // 添加滤波后的速度值
} Encoder;

void Encoder_Driver_Init(Encoder* encoder, TIM_HandleTypeDef *htim, unsigned char reverse);
void Encoder_Driver_Update(Encoder* encoder);

extern Encoder left_encoder;
extern Encoder right_encoder;

#endif
