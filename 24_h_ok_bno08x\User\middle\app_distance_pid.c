#include "app_distance_pid.h"//定距pid

PID distance_pid;

//获取任务函数中设定的目标角
void task_set_target_angle(int target_angle)
{
	distance_pid.target = target_angle;
}

void distance_pid_init(void)
{
    pid_init(&distance_pid, 75, 2, 10, 1000, 1000, DEFAULT_ANGLE );
}
PID* get_distance_pid(void)
{
	return &distance_pid;
}
int get_distance_pid_target(void)
{
	return distance_pid.target;
}


PID motor_distance_control(float target, int get_encoder)
{
	int target_pulses = 0;
//	char buff[50]={0};

	//将角度转换为脉冲
	target_pulses = (target * 1.0f) / DEGREES_PER_PULSE;

	pid_calc(&distance_pid, target_pulses, get_encoder);

	return distance_pid;   
}
