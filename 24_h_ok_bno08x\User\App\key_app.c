#include "key_app.h"

extern UART_HandleTypeDef huart1;

extern uint8_t led_rgb[5];
extern bool pid_running;

uint8_t runing_flat = 0;
uint16_t runing_time = 0;

uint8_t test = 0;
extern uint8_t first_flat;

extern unsigned char system_mode;

//extern uint8_t stop_flag;
extern float g_last_yaw;
extern int g_revolution_count;
extern bool g_is_yaw_initialized;

void key_task(void)
{
    static uint8_t key_old = 0;

    uint8_t key_val = key_read();
    uint8_t key_down = key_val & (key_val ^ key_old);
//	uint8_t key_up = ~key_val & (key_val ^ key_old);
    key_old = key_val;

//	if(runing_flat)
//	{
//		static uint16_t num = 0;
//		static uint8_t temp = 0;
//		if(++temp >= 10)
//		{
//			temp = 0;
//			num += 5;
//		}
//
//		pid_set_target(&pid_speed_left, num);
//    pid_set_target(&pid_speed_right, num);
//	}
//
//	if(runing_flat)
//	{
//		if(++runing_time >= 200)
//		{
//			runing_flat = 0;
//			runing_time = 0;
//			num = 0;
//			motor_set_l(0);
//			motor_set_r(0);
//			pid_running = 0;
//		}
//	}
//
//	if(uwTick%1000 <= 10)
//		my_printf(&huart1, "%d\r\n", uwTick);

    switch(key_down)
    {
    case 1://Key1 -
        pid_running = 1;
        stop_flag = 0;
        State_Machine.Main_State = QUESTION_1;
		
//				pid_set_target(&pid_speed_left, 15);
//				pid_set_target(&pid_speed_right, 15);
        break;
    case 2://Key2
        led_rgb[0] = 1;
        led_rgb[1] = 0;
        led_rgb[2] = 0;

//			pid_set_target(&pid_speed_left, 90);
//			pid_set_target(&pid_speed_right, 90);

//			motor_set_l(100);
//			motor_set_r(100);
//			runing_flat = 1;
//			pid_running = 1;
        break;
    case 3://Key3 -
        led_rgb[0] = 0;
        led_rgb[1] = 1;
        led_rgb[2] = 1;

        system_mode = (system_mode + 1)%5;

//			first_flat = 0;
//			g_last_yaw = 0.0f;
//			g_revolution_count = 0;
//			g_is_yaw_initialized = false;
//			pid_reset(&pid_line);
//			pid_reset(&pid_angle);
//			bno080_task();

        break;
    case 4://Key4 -



        break;
    case 10://User Key -



        break;
    }
}
