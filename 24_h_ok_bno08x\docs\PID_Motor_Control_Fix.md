# 步进电机PID控制问题修复文档

## 问题描述
用户反映在测试步进电机代码时，直接给pi_proc里面赋值后，电机一直在转动，无法停止。

## 问题分析

### 根本原因
1. **PID参数未初始化**：`pid_x` 和 `pid_y` 结构体没有被正确初始化，所有参数都是0或随机值
2. **积分累积问题**：由于PID参数不正确，积分项在不断累积，导致输出持续增大
3. **缺少停止条件**：`pi_proc()` 在main函数中只调用一次，但PID输出会持续驱动电机

### 原始问题代码
```c
void pi_proc(void)
{
    float pos_out_x,pos_out_y=0;
    
    pos_out_x = pid_calc(&pid_x,0,0);  // PID未初始化
    pos_out_y = pid_calc(&pid_y,9,5);  // PID未初始化
    my_printf(&huart1, "Parsed GRE: XX=%d, Y=%d\r\n",pos_out_y);
    Step_Motor_Set_Speed_my(-pos_out_x,pos_out_y);  // 持续输出到电机
}
```

## 解决方案

### 方案1：PID参数初始化（主要修复）
- 在`pi_proc()`函数中添加PID参数初始化
- 设置合理的PID参数：kp=1.0, ki=0.1, kd=0.05
- 添加积分限幅和输出限幅防止饱和
- 添加死区控制避免小幅震荡

### 方案2：添加电机停止机制
- 在main函数中添加延时和停止指令
- 让PID有时间稳定后主动停止电机

### 方案3：PID参数调节功能
- 添加`pi_set_pid_params()`函数用于运行时调节PID参数
- 添加`pi_stop_motors()`函数用于紧急停止

## 修改内容

### 文件：pi_bsp.c
1. 添加数学库头文件支持
2. 在pi_proc()中添加PID初始化逻辑
3. 添加死区控制
4. 新增PID参数调节函数
5. 新增电机停止函数

### 文件：pi_bsp.h
1. 添加新函数声明

### 文件：main.c
1. 在pi_proc()调用后添加延时和停止指令

## PID参数调节指南

### 推荐的初始参数
- **比例系数(kp)**：1.0 - 控制响应速度
- **积分系数(ki)**：0.1 - 消除稳态误差
- - **微分系数(kd)**：0.05 - 减少超调

### 参数调节方法
1. **如果电机震荡**：减小kp值
2. **如果响应太慢**：增大kp值
3. **如果有稳态误差**：增大ki值
4. **如果超调严重**：增大kd值

### 使用新的调节函数
```c
// 调节X轴PID参数
pi_set_pid_params('x', 0.8f, 0.05f, 0.02f);

// 调节Y轴PID参数  
pi_set_pid_params('y', 1.2f, 0.15f, 0.08f);

// 紧急停止电机
pi_stop_motors();
```

## 测试建议
1. 编译并下载程序
2. 观察串口输出的PID数值
3. 根据电机表现调节PID参数
4. 使用pi_stop_motors()函数测试紧急停止功能

## 注意事项
- PID参数需要根据实际机械系统特性调节
- 积分项会累积，长时间运行需要定期清零
- 死区设置为0.5，可根据需要调整
- 建议在调试时降低电机最大速度限制
