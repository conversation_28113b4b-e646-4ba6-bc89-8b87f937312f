# 蓝紫光检测功能使用说明

## 概述

本功能为您的激光跟踪系统增加了**蓝紫光检测与坐标显示**功能，可以实时检测视野中的蓝紫色激光点并显示其精确坐标，用于动态设置目标跟踪点。

## 文件说明

### 1. `enhanced_laser_tracking.py` - 完整增强版
- **功能**: 在原有矩形检测基础上增加蓝紫光检测
- **特点**: 保留所有原有功能，无缝集成激光检测
- **适用**: 需要同时使用矩形跟踪和激光检测的场景

### 2. `laser_detection_demo.py` - 专用演示版  
- **功能**: 专门用于蓝紫光检测的简化版本
- **特点**: 界面简洁，专注激光点检测和坐标显示
- **适用**: 只需要激光检测功能的场景

### 3. `laser_config.py` - 配置文件
- **功能**: 提供多种预设配置，适应不同环境
- **特点**: 包含室内、室外、暗光等多种环境配置
- **适用**: 需要在不同环境下优化检测效果

## 核心功能

### 🎯 激光点检测
- **颜色识别**: 基于HSV颜色空间精确识别蓝紫色
- **噪声过滤**: 使用形态学操作去除干扰
- **多点检测**: 可同时检测多个激光点
- **面积过滤**: 根据面积大小过滤无效检测

### 📍 坐标显示
- **实时坐标**: 在图像上实时显示激光点坐标
- **主点标记**: 自动识别面积最大的激光点作为主要目标
- **精度显示**: 显示检测面积等详细信息
- **控制台输出**: 定期在控制台输出主要坐标信息

### 🔧 参数调节
- **HSV范围调节**: 支持实时调整颜色检测范围
- **面积过滤**: 可调节最小/最大激光点面积
- **预设配置**: 提供多种环境预设配置

## 使用方法

### 快速开始

1. **运行演示程序**:
```bash
python laser_detection_demo.py
```

2. **运行完整版本**:
```bash
python enhanced_laser_tracking.py
```

### 配置优化

1. **选择合适的预设配置**:
```python
from laser_config import get_config

# 室内环境
config = get_config('indoor')

# 室外环境  
config = get_config('outdoor')

# 暗光环境
config = get_config('low_light')

# 高精度模式
config = get_config('high_precision')
```

2. **应用配置到检测器**:
```python
detector.hsv_lower = config['hsv_lower']
detector.hsv_upper = config['hsv_upper']
```

## 检测参数说明

### HSV颜色范围
- **色调(H)**: 120-160 (蓝紫色范围)
- **饱和度(S)**: 50-255 (颜色纯度)
- **亮度(V)**: 50-255 (明暗程度)

### 面积过滤
- **最小面积**: 10像素 (过滤噪点)
- **最大面积**: 500像素 (过滤大面积干扰)

### 图像处理
- **高斯模糊**: 5x5核 (减少噪声)
- **形态学操作**: 3x3核 (连接断裂区域)

## 环境配置建议

### 🏠 室内环境
- **光照**: 均匀室内照明
- **配置**: `get_config('indoor')`
- **特点**: 标准HSV范围，适中的面积过滤

### 🌞 室外环境  
- **光照**: 强烈自然光
- **配置**: `get_config('outdoor')`
- **特点**: 更严格的颜色范围，更大的面积过滤

### 🌙 暗光环境
- **光照**: 昏暗或夜间
- **配置**: `get_config('low_light')`
- **特点**: 扩大HSV范围，降低亮度要求

### 🎯 高精度模式
- **需求**: 检测小激光点
- **配置**: `get_config('high_precision')`
- **特点**: 最小面积过滤，精细检测

## 界面说明

### 显示元素
- **黄色大圆**: 主要激光点（面积最大）
- **紫色圆圈**: 次要激光点
- **坐标标签**: 显示精确的(x,y)坐标
- **面积信息**: 显示检测到的像素面积
- **检测掩码**: 右上角小窗口显示颜色过滤结果

### 信息面板
- **FPS**: 实时帧率显示
- **激光点数量**: 当前检测到的激光点总数
- **主点坐标**: 主要激光点的坐标和面积

## 集成到您的系统

### 1. 替换原有检测器
```python
# 原来的代码
laser_detector = PurpleLaserDetector()

# 替换为增强版
from enhanced_laser_tracking import PurpleLaserDetector
laser_detector = PurpleLaserDetector()
```

### 2. 获取激光点坐标
```python
output, laser_points = laser_detector.detect(img)

if laser_points:
    # 获取主要激光点坐标
    main_x, main_y = laser_points[0][0], laser_points[0][1]
    
    # 更新目标点
    target_x, target_y = main_x, main_y
```

### 3. 串口数据发送
```python
if laser_points:
    main_point = laser_points[0]
    x, y = main_point[0], main_point[1]
    micu_printf(f"L,{x},{y}")  # 发送激光点坐标
```

## 故障排除

### 检测不到激光点
1. **检查颜色范围**: 激光颜色是否在蓝紫色范围内
2. **调整HSV参数**: 使用`adjust_detection_range()`方法
3. **检查面积过滤**: 激光点可能太小或太大
4. **环境光照**: 确保有足够的对比度

### 误检测过多
1. **缩小HSV范围**: 使用更严格的颜色过滤
2. **增加面积过滤**: 提高最小面积阈值
3. **增强形态学操作**: 使用更大的核进行噪声过滤

### 检测不稳定
1. **增加高斯模糊**: 使用更大的模糊核
2. **调整曝光**: 如果可能，调整摄像头曝光设置
3. **使用宽容配置**: `get_config('tolerant')`

## 性能优化

### 提高检测速度
- 减小图像分辨率
- 降低处理频率
- 使用更简单的形态学操作

### 提高检测精度
- 增加图像分辨率
- 使用更精细的HSV范围
- 增加形态学操作步骤

## 技术支持

如果您在使用过程中遇到问题，请检查：

1. **摄像头是否正常工作**
2. **激光器是否发出蓝紫色光**
3. **环境光照是否合适**
4. **配置参数是否适合当前环境**

---

**版本**: v1.0  
**更新日期**: 2025-01-01  
**作者**: Alex (工程师)