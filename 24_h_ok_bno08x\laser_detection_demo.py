"""
蓝紫光检测演示程序
专门用于检测和显示蓝紫色激光点坐标
"""

from maix import image, display, app, time, camera, touchscreen
import cv2
import numpy as np
import gc

# 蓝紫光检测参数配置
PURPLE_HSV_LOWER = np.array([120, 50, 50])    # 蓝紫色HSV下限 (色调120-160)
PURPLE_HSV_UPPER = np.array([160, 255, 255])  # 蓝紫色HSV上限
MIN_LASER_AREA = 10          # 最小激光点面积
MAX_LASER_AREA = 500         # 最大激光点面积
LASER_BLUR_KERNEL = 5        # 高斯模糊核大小

class LaserPointDetector:
    def __init__(self):
        self.hsv_lower = PURPLE_HSV_LOWER.copy()
        self.hsv_upper = PURPLE_HSV_UPPER.copy()
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect_laser_points(self, img):
        """检测蓝紫色激光点并返回坐标"""
        laser_points = []
        
        try:
            # 1. 转换到HSV颜色空间
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            
            # 2. 高斯模糊减少噪声
            blurred = cv2.GaussianBlur(hsv, (LASER_BLUR_KERNEL, LASER_BLUR_KERNEL), 0)
            
            # 3. 创建蓝紫色掩码
            mask = cv2.inRange(blurred, self.hsv_lower, self.hsv_upper)
            
            # 4. 形态学操作去除噪声
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, self.kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, self.kernel)
            
            # 5. 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 6. 分析每个轮廓
            for contour in contours:
                area = cv2.contourArea(contour)
                
                # 面积过滤
                if MIN_LASER_AREA <= area <= MAX_LASER_AREA:
                    # 计算质心坐标
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        
                        # 计算轮廓的边界框
                        x, y, w, h = cv2.boundingRect(contour)
                        
                        laser_points.append({
                            'center': (cx, cy),
                            'area': area,
                            'bbox': (x, y, w, h),
                            'contour': contour
                        })
            
            # 按面积排序，面积大的在前
            laser_points.sort(key=lambda x: x['area'], reverse=True)
            
            return laser_points, mask
            
        except Exception as e:
            print(f"激光检测错误: {e}")
            return [], np.zeros((img.shape[0], img.shape[1]), dtype=np.uint8)
    
    def draw_laser_points(self, img, laser_points):
        """在图像上绘制检测到的激光点"""
        for i, point in enumerate(laser_points):
            cx, cy = point['center']
            area = point['area']
            
            # 绘制激光点标记
            if i == 0:  # 主激光点（面积最大）
                cv2.circle(img, (cx, cy), 10, (0, 255, 255), 2)  # 黄色大圆
                cv2.circle(img, (cx, cy), 4, (255, 0, 255), -1)  # 紫色实心点
                label = f"MAIN({cx},{cy})"
                color = (0, 255, 255)
            else:  # 次要激光点
                cv2.circle(img, (cx, cy), 6, (255, 0, 255), 2)   # 紫色圆圈
                cv2.circle(img, (cx, cy), 2, (0, 255, 0), -1)    # 绿色实心点
                label = f"({cx},{cy})"
                color = (255, 0, 255)
            
            # 显示坐标标签
            cv2.putText(img, label, (cx + 12, cy - 8),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            
            # 显示面积信息
            area_text = f"A:{int(area)}"
            cv2.putText(img, area_text, (cx + 12, cy + 8),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
        
        return img
    
    def adjust_detection_range(self, h_offset=0, s_offset=0, v_offset=0):
        """动态调整HSV检测范围"""
        # 调整色调范围
        self.hsv_lower[0] = max(0, min(179, self.hsv_lower[0] + h_offset))
        self.hsv_upper[0] = max(0, min(179, self.hsv_upper[0] + h_offset))
        
        # 调整饱和度范围
        self.hsv_lower[1] = max(0, min(255, self.hsv_lower[1] + s_offset))
        self.hsv_upper[1] = max(0, min(255, self.hsv_upper[1] + s_offset))
        
        # 调整亮度范围
        self.hsv_lower[2] = max(0, min(255, self.hsv_lower[2] + v_offset))
        self.hsv_upper[2] = max(0, min(255, self.hsv_upper[2] + v_offset))
        
        print(f"HSV范围调整为: Lower{self.hsv_lower}, Upper{self.hsv_upper}")

def draw_ui_info(img, laser_points, fps):
    """绘制用户界面信息"""
    # 背景半透明区域
    overlay = img.copy()
    cv2.rectangle(overlay, (5, 5), (315, 100), (0, 0, 0), -1)
    cv2.addWeighted(overlay, 0.3, img, 0.7, 0, img)
    
    # 标题
    cv2.putText(img, "Blue-Purple Laser Detection", (10, 25),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # FPS信息
    cv2.putText(img, f"FPS: {fps:.1f}", (10, 45),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
    
    # 激光点数量
    cv2.putText(img, f"Laser Points: {len(laser_points)}", (10, 65),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)
    
    # 主激光点坐标
    if laser_points:
        main_point = laser_points[0]
        cx, cy = main_point['center']
        area = main_point['area']
        cv2.putText(img, f"Main: ({cx},{cy}) Area:{int(area)}", (10, 85),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
    else:
        cv2.putText(img, "No laser detected", (10, 85),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)

def draw_detection_mask(img, mask):
    """在图像角落显示检测掩码"""
    # 缩放掩码到小窗口
    mask_small = cv2.resize(mask, (80, 60))
    mask_bgr = cv2.cvtColor(mask_small, cv2.COLOR_GRAY2BGR)
    
    # 在右上角显示
    img[10:70, 230:310] = mask_bgr
    cv2.rectangle(img, (230, 10), (310, 70), (255, 255, 255), 1)
    cv2.putText(img, "Mask", (235, 85), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)

# 主程序
if __name__ == "__main__":
    gc.disable()
    print("蓝紫光检测程序启动...")
    print("功能：实时检测蓝紫色激光点并显示坐标")
    
    # 初始化设备
    disp = display.Display()
    cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
    detector = LaserPointDetector()
    
    # FPS计算
    fps = 0
    last_time = time.ticks_ms()
    frame_count = 0
    
    print("程序运行中，按Ctrl+C退出...")
    print(f"当前HSV检测范围: {detector.hsv_lower} - {detector.hsv_upper}")
    
    while not app.need_exit():
        frame_count += 1
        
        # 计算FPS
        current_time_ms = time.ticks_ms()
        if current_time_ms - last_time > 0:
            fps = 1000.0 / (current_time_ms - last_time)
        last_time = current_time_ms
        
        # 读取图像
        img = cam.read()
        if img is None:
            continue
            
        img_cv = image.image2cv(img, ensure_bgr=False, copy=False)
        output = img_cv.copy()
        
        # 检测激光点
        laser_points, mask = detector.detect_laser_points(output)
        
        # 绘制激光点
        output = detector.draw_laser_points(output, laser_points)
        
        # 绘制UI信息
        draw_ui_info(output, laser_points, fps)
        
        # 显示检测掩码
        draw_detection_mask(output, mask)
        
        # 在控制台输出主要激光点坐标（每30帧输出一次）
        if frame_count % 30 == 0 and laser_points:
            main_point = laser_points[0]
            cx, cy = main_point['center']
            area = main_point['area']
            print(f"主激光点坐标: ({cx}, {cy}), 面积: {area:.1f}")
        
        # 显示图像
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)
        
        # 短暂延时
        time.sleep(0.01)
    
    print("程序结束")