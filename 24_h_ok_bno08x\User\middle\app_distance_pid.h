#ifndef	__APP_DISTANCE_PID_H__
#define __APP_DISTANCE_PID_H__
#include "app_distance_pid.h"//定距pid
/*HAL库*/
#include "main.h"
#include "mid_pid.h"

//目标脉冲数 = ( 目标距离 / 轮子周长 ) × 编码器每转脉冲数 × 减速比
//示例：轮子直径10cm → 周长=31.4cm，编码器1000脉冲/转，减速比10:1 → 1米对应脉冲数 = ( 100 / 31.4 ) × 1000 × 10 ≈ 31847
//示例：轮子直径65mm → 周长=204.20mm，编码器2496脉冲/转，减速比48:1 → 1米对应脉冲数 = ( 1,000 / 204.20 ) × 2496 × 48 ≈ 31847

#define MOTOR_REDUCTION_RATIO   20    //电机减速比 1：20
#define ENCODER_RESOLUTION  	260     //编码器分辨率 1248脉冲/转
#define TIRE_CIRCUMFERENCE 		150.796   //轮胎周长单位为毫米 TIRE_DIAMETER x 3.1415926
#define TIRE_DIAMETER  			48         //轮胎直径 单位毫米

#define DEGREES_PER_PULSE  		1.385	// 每脉冲度数 360 / ENCODER_RESOLUTION
#define DEFAULT_ANGLE			90			// 起始默认旋转角度

void distance_pid_init(void);
PID motor_distance_control(float target, int get_encoder);
PID* get_distance_pid(void);
void task_set_target_angle(int target_angle);
int get_distance_pid_target(void);
#endif
